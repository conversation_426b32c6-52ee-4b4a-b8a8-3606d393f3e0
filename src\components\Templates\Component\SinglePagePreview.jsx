import React, { useEffect, useRef, useState } from "react";
import { FileText } from "lucide-react";
import { generateGlobalPreviewHTML } from "../../Components/content";
import { useDragLayer } from "react-dnd";

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const SinglePagePreview = ({ templatePage, previewMode, isDragging }) => {
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);
  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];

  // Scale calculation function (similar to PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - (previewMode == "laptop" ? 15 : 15); // 150px padding on each side
    const availableHeight = bounds.height - (previewMode == "laptop" ? 15 : 15); // 150px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    requestAnimationFrame(() => {
      setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
    });
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  // Template Page Preview Component - Single device frame like PagePreview
  const TemplatePagePreview = ({ templatePage }) => {
    const generatePagePreviewHTML = () => {
      if (!templatePage) return "";
      return generateGlobalPreviewHTML({
        type: "page",
        data: [],
        // pageData: templatePage,
        customCSS: templatePage.custom_css,
        customJS: templatePage.custom_js,
        title: templatePage.name || templatePage.name,
        fullData: templatePage?.full_page_content || "",
      });
    };

    return (
      <div
        className={`tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative `}
        style={{
          height: "auto",
          minHeight:
            previewMode == "laptop"
              ? "320px"
              : previewMode == "tablet"
              ? "490px"
              : "490px",
        }}
      >
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl  tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "50%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${
              deviceHeight /
              (previewMode == "laptop"
                ? 2.4
                : previewMode == "tablet"
                ? 2.4
                : 2.9)
            }px`,
            transition: "all 0.3s ease",
            zIndex: isDragging ? 0 : 25,
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-rounded-xl">
            {templatePage ? (
              <iframe
                // srcDoc={templatePage?.full_page_content}
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl tw-relative"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: isDragging ? "none" : "auto",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      style={{
        height: "auto",
        minHeight:
          previewMode == "laptop"
            ? `${29}rem`
            : previewMode == "tablet"
            ? `${42}rem`
            : `${43}rem`,
      }}
      className={`tw-w-full  tw-overflow-auto tw-relative tw-rounded-lg `}
    >
      <TemplatePagePreview
        key={templatePage.id}
        templatePage={templatePage}
        // originalPage={originalPage}
      />
    </div>
  );
};

export default SinglePagePreview;
