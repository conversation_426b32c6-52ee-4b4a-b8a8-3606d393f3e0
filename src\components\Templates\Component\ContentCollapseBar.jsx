import React, { useCallback, useMemo, useRef, useState } from "react";
import SearchBar from "../../common/SearchBar";
import { Button, Collapse, Input, message } from "antd";
import JsonContentCollapse from "../../common/JsonContentCollapse";
// import { buildItemList } from "../../../util/functions";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentCollapseBar = ({ contentJSON, setContentJSON, saving }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedAll, setExpandedAll] = useState(false);
  const [importing, setImporting] = useState(false);
  const fileInputRef = useRef(null);

  //   const itemList = useMemo(() => {
  //     const listObj = contentJSON || {};
  //     console.log(listObj, "listObj");
  //     return Object.keys(listObj)?.map((key) => ({
  //       key,
  //       label: key,
  //       extra: Array.isArray(listObj[key]),
  //       children: !Array.isArray(listObj[key]) ? (
  //         <>
  //           <JsonContentCollapse
  //             expanded={expandedAll}
  //             itemList={Object.keys(listObj[key])?.map((keyChild) => ({
  //               key: keyChild,
  //               label: keyChild,
  //               isDelete: true,
  //               // onDelete: () => handleDeleteField(key, keyChild),
  //               onDelete: () => handleDelete(key, null, keyChild),
  //               children: (
  //                 <>
  //                   {console.log(
  //                     listObj[key][keyChild],
  //                     "listObj[key][keyChild]"
  //                   )}
  //                   {typeof listObj[key][keyChild] === "object" ? (
  //                     <JsonContentCollapse
  //                       expanded={expandedAll}
  //                       itemList={Object.keys(listObj[key][keyChild])?.map(
  //                         (keyChilds) => ({
  //                           key: keyChilds,
  //                           label: keyChilds,
  //                           isDelete: true,
  //                           children: (
  //                             <TextArea
  //                               rows={4}
  //                               value={listObj[key][keyChild][keyChilds]}
  //                               readOnly
  //                             />
  //                           ),
  //                         })
  //                       )}
  //                     />
  //                   ) : (
  //                     <TextArea
  //                       rows={4}
  //                       value={listObj[key][keyChild]}
  //                       readOnly
  //                     />
  //                   )}
  //                 </>
  //               ),
  //             }))}
  //           />
  //         </>
  //       ) : (
  //         <>
  //           <JsonContentCollapse
  //             expanded={expandedAll}
  //             itemList={listObj[key]?.map((keyChild, index) => ({
  //               key: "Position_" + index,
  //               label: "Position " + (index + 1),
  //               // isDelete: true,
  //               // onDelete: () => handleDeletePosition(key, index),
  //               //  onDelete: () => handleDelete(key, index),
  //               children: (
  //                 <JsonContentCollapse
  //                   expanded={expandedAll}
  //                   // onDeleteItem={(it) =>
  //                   //   handleDeletePositionField(key, index, it.key)
  //                   // }
  //                   onDeleteItem={(it) => handleDelete(key, index, it.key)}
  //                   itemList={Object.keys(keyChild)?.map((keyChilds) => ({
  //                     key: keyChilds,
  //                     label: keyChilds,
  //                     isDelete: true,
  //                     children: (
  //                       <TextArea rows={4} value={keyChild[keyChilds]} readOnly />
  //                     ),
  //                   }))}
  //                 />
  //               ),
  //             }))}
  //           />
  //           {Array.isArray(listObj[key]) && listObj[key].length > 0 && (
  //             <div className="tw-mt-4 tw-text-center">
  //               <Button
  //                 type="dashed"
  //                 onClick={() => handleAddItem(key)}
  //                 className="tw-w-full tw-h-10 tw-border-2 tw-border-dashed tw-border-gray-300 hover:tw-border-blue-400 tw-text-gray-600 hover:tw-text-blue-600"
  //               >
  //                 + Add Item
  //               </Button>
  //             </div>
  //           )}
  //         </>
  //       ),
  //     }));
  //   }, [contentJSON, expandedAll, searchTerm]);

  const handleAddItem = (sectionKey) => {
    try {
      const section = contentJSON?.[sectionKey];
      if (!Array.isArray(section)) return;
      if (section.length === 0) {
        message.warning("Cannot add item: reference structure not found.");
        return;
      }
      const referenceKeys = Object.keys(section[0] || {});
      const newItem = referenceKeys.reduce(
        (acc, k) => ({ ...acc, [k]: "" }),
        {}
      );
      setContentJSON((prev) => ({
        ...prev,
        [sectionKey]: [...prev[sectionKey], newItem],
      }));
      message.success("Item added");
    } catch (e) {
      console.error(e);
      message.error("Failed to add item");
    }
  };

  const handleDelete = useCallback(
    (sectionKey, index = null, fieldKey = null) => {
      try {
        setContentJSON((prev) => {
          const section = prev?.[sectionKey];
          if (!section) return prev;

          // If index is provided, we're dealing with an array item
          if (index !== null) {
            // If fieldKey is also provided, delete a field from an array item
            if (fieldKey !== null) {
              if (!Array.isArray(section)) return prev;

              const list = [...section];
              if (index >= list.length) return prev;

              const item = list[index];
              if (!item) return prev;

              const updatedItem = { ...item };
              delete updatedItem[fieldKey];
              list[index] = updatedItem;

              return { ...prev, [sectionKey]: list };
            }
            // If no fieldKey, delete the entire array item
            else {
              if (!Array.isArray(section)) return prev;

              const list = [...section];
              if (index >= list.length) return prev;

              list.splice(index, 1);
              return { ...prev, [sectionKey]: list };
            }
          }
          // If no index, delete a field from an object
          else if (fieldKey !== null) {
            if (Array.isArray(section)) return prev;

            const updatedSection = { ...section };
            delete updatedSection[fieldKey];
            return { ...prev, [sectionKey]: updatedSection };
          }

          return prev;
        });

        message.success("Item deleted successfully");
      } catch (e) {
        console.error(e);
        message.error("Failed to delete item");
      }
    },
    []
  );
  const buildItemList = (
    data,
    expandedAll,
    parentKey,
    handleDelete,
    handleAddItem
  ) => {
    if (Array.isArray(data)) {
      // If data is an array
      console.log(data);
      return data.map((item, index) => ({
        key: `${parentKey}_pos_${index}`,
        label: `Position ${index + 1}`,
        children: (
          <>
            <JsonContentCollapse
              expanded={expandedAll}
              onDeleteItem={(it) => handleDelete(parentKey, index, it.key)}
              itemList={buildItemList(
                item,
                expandedAll,
                parentKey,
                handleDelete,
                handleAddItem
              )}
            />
            {Array.isArray(data) && data.length > 0 && (
              <div className="tw-mt-4 tw-text-center">
                <Button
                  type="dashed"
                  onClick={() => handleAddItem(parentKey)}
                  className="tw-w-full tw-h-10 tw-border-2 tw-border-dashed tw-border-gray-300 hover:tw-border-blue-400 tw-text-gray-600 hover:tw-text-blue-600"
                >
                  + Add Item
                </Button>
              </div>
            )}
          </>
        ),
      }));
    } else if (typeof data === "object" && data !== null) {
      // If data is an object
      return Object.keys(data).map((key) => ({
        key,
        label: key,
        isDelete: true,
        onDelete: () => handleDelete(parentKey, null, key),
        children:
          typeof data[key] === "object" ? (
            <>
              {console.log(data[key], "data[key]")}
              <JsonContentCollapse
                expanded={expandedAll}
                itemList={buildItemList(
                  data[key],
                  expandedAll,
                  key,
                  handleDelete,
                  handleAddItem
                )}
              />
               {Array.isArray(data) && data.length > 0 && (
              <div className="tw-mt-4 tw-text-center">
                <Button
                  type="dashed"
                  onClick={() => handleAddItem(parentKey)}
                  className="tw-w-full tw-h-10 tw-border-2 tw-border-dashed tw-border-gray-300 hover:tw-border-blue-400 tw-text-gray-600 hover:tw-text-blue-600"
                >
                  + Add Item
                </Button>
              </div>
            )}
            </>
          ) : (
            
            <TextArea rows={4} value={data[key]} readOnly />
          ),

      }));
    } else {
      // Primitive values
      return [
        {
          key: parentKey,
          label: parentKey,
          children: React.createElement(TextArea, {
            rows: 4,
            value: data,
            readOnly: true,
          }),
        },
      ];
    }
  };

  const itemList = useMemo(() => {
    if (!contentJSON) return [];
    return Object.keys(contentJSON).map((key) => ({
      key,
      label: key,
      extra: Array.isArray(contentJSON[key]),
      children: (
        <>
          <JsonContentCollapse
            expanded={expandedAll}
            itemList={buildItemList(
              contentJSON[key],
              expandedAll,
              key,
              handleDelete,
              handleAddItem
            )}
          />
          {console.log(contentJSON[key])}
          {Array.isArray(contentJSON[key]) && contentJSON[key].length > 0 && (
            <div className="tw-mt-4 tw-text-center">
              <Button
                type="dashed"
                onClick={() => handleAddItem(key)}
                className="tw-w-full tw-h-10 tw-border-2 tw-border-dashed tw-border-gray-300 hover:tw-border-blue-400 tw-text-gray-600 hover:tw-text-blue-600"
              >
                + Add Item
              </Button>
            </div>
          )}
        </>
      ),
    }));
  }, [contentJSON, expandedAll, handleDelete, handleAddItem]);

  const toggleExpandAll = () => {
    setExpandedAll((prev) => !prev);
  };

  const handleImportJSON = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const onFileChange = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setImporting(true);
    try {
      const text = await file.text();
      const parsed = JSON.parse(text);
      if (!parsed || typeof parsed !== "object" || Array.isArray(parsed)) {
        throw new Error("Invalid JSON structure. Expected an object at root.");
      }
      setContentJSON(parsed);
      message.success("JSON imported successfully");
    } catch (err) {
      console.error("Import JSON error:", err);
      message.error("Failed to import JSON. Please check the file.");
    } finally {
      setImporting(false);
      // Reset input value to re-trigger change event if same file is selected again
      if (fileInputRef.current) fileInputRef.current.value = "";
    }
  };
  return (
    <>
      <div className="tw-p-4  ">
        <div>
          <SearchBar handleSearch={(e) => setSearchTerm(e)} />
        </div>
        <div className="tw-flex tw-space-x-4 tw-w-full tw-justify-between tw-items-center">
          <Button
            type="primary"
            size="large"
            onClick={handleImportJSON}
            disabled={saving || importing}
            className="tw-px-6 tw-w-full tw-h-10 tw-flex tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
          >
            Import JSON
          </Button>

          <Button
            size="large"
            onClick={toggleExpandAll}
            className="tw-flex tw-w-full tw-items-center tw-text-black tw-border-blue-200 hover:tw-bg-blue-50"
          >
            {expandedAll ? "Collapse All" : "Expand All"}
          </Button>
        </div>
        {/* Hidden file input for JSON import */}
        <input
          type="file"
          accept="application/json,.json"
          ref={fileInputRef}
          onChange={onFileChange}
          style={{ display: "none" }}
        />
      </div>
      <div className="tw-flex-1 tw-overflow-auto tw-p-4">
        {itemList?.length ? (
          <JsonContentCollapse itemList={itemList} expanded={expandedAll} />
        ) : (
          <div className="tw-text-center tw-py-8">
            <p className="tw-text-gray-500 tw-mb-2">No content fields found</p>
            <p className="tw-text-sm tw-text-gray-400">
              Add content fields to see them here
            </p>
          </div>
        )}
      </div>
    </>
  );
};

export default ContentCollapseBar;
